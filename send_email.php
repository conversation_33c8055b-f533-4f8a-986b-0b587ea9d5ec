<?php
// Gmail SMTP Email Handler using PHPMailer
// This is a more robust solution for sending emails via Gmail

// Load PHPMailer classes if available
if (file_exists('PHPMailer/src/PHPMailer.php')) {
    require_once 'PHPMailer/src/PHPMailer.php';
    require_once 'PHPMailer/src/SMTP.php';
    require_once 'PHPMailer/src/Exception.php';
}

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
    exit;
}

// Get and sanitize form data
$name = strip_tags(trim($_POST["name"] ?? ''));
$email = filter_var(trim($_POST["email"] ?? ''), FILTER_SANITIZE_EMAIL);
$subject = strip_tags(trim($_POST["subject"] ?? ''));
$message = trim($_POST["message"] ?? '');

// Validate required fields
if (empty($name) || empty($email) || empty($subject) || empty($message)) {
    echo json_encode(['success' => false, 'message' => 'Please fill in all fields.']);
    exit;
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    echo json_encode(['success' => false, 'message' => 'Please enter a valid email address.']);
    exit;
}

// Try to use PHPMailer if available
if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    
    try {
        $mail = new PHPMailer(true);
        
        // Gmail SMTP configuration
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>'; // Replace with your Gmail address
        $mail->Password = 'your-app-password';    // Replace with your Gmail App Password (https://support.google.com/mail/answer/185833?hl=en)
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'SantéBlock Contact Form');
        $mail->addAddress('<EMAIL>', 'SantéBlock Team');
        $mail->addReplyTo($email, $name);
        
        // Content
        $mail->isHTML(false);
        $mail->Subject = "New Contact Message from $name";
        $mail->Body = "You received a new message from your website contact form:\n\n" .
                      "Name: $name\n" .
                      "Email: $email\n" .
                      "Subject: $subject\n\n" .
                      "Message:\n$message\n\n" .
                      "---\n" .
                      "Sent from SantéBlock Contact Form";
        
        $mail->send();
        echo json_encode(['success' => true, 'message' => 'Thank you! Your message has been sent successfully.']);
        
    } catch (Exception $e) {
        error_log("PHPMailer Error: " . $mail->ErrorInfo);
        echo json_encode(['success' => false, 'message' => 'Sorry, there was an error sending your message. Please try again later.']);
    }
    
} else {
    // Fallback to simple mail() function
    $to_email = "<EMAIL>";
    $email_subject = "New Contact Message from $name";
    $email_body = "You received a new message from your website contact form:\n\n" .
                  "Name: $name\n" .
                  "Email: $email\n" .
                  "Subject: $subject\n\n" .
                  "Message:\n$message\n\n" .
                  "---\n" .
                  "Sent from SantéBlock Contact Form";
    
    $headers = array(
        'From' => "SantéBlock Contact Form <<EMAIL>>",
        'Reply-To' => "$name <$email>",
        'X-Mailer' => 'PHP/' . phpversion(),
        'Content-Type' => 'text/plain; charset=UTF-8'
    );
    
    $headers_string = implode("\r\n", $headers);
    
    if (mail($to_email, $email_subject, $email_body, $headers_string)) {
        echo json_encode(['success' => true, 'message' => 'Thank you! Your message has been sent successfully.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Sorry, there was an error sending your message. Please try again later.']);
    }
}
?>
