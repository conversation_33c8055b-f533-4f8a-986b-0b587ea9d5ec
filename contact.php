<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type to JSON
header('Content-Type: application/json');

// Check if <PERSON><PERSON><PERSON>ail<PERSON> exists, if not, use simple mail
$use_phpmailer = class_exists('<PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer');

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get and sanitize form data
    $name = strip_tags(trim($_POST["name"]));
    $email = filter_var(trim($_POST["email"]), FILTER_SANITIZE_EMAIL);
    $subject = strip_tags(trim($_POST["subject"]));
    $message = trim($_POST["message"]);

    // Validate required fields
    if (empty($name) || empty($email) || empty($subject) || empty($message)) {
        echo json_encode(['success' => false, 'message' => 'Please fill in all fields.']);
        exit;
    }

    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Please enter a valid email address.']);
        exit;
    }

    // Email configuration
    $to_email = "<EMAIL>"; // Updated to use the email from footer
    $email_subject = "New Contact Message from $name";
    $email_body = "You received a new message from your website contact form:\n\n".
                  "Name: $name\n".
                  "Email: $email\n".
                  "Subject: $subject\n\n".
                  "Message:\n$message\n\n".
                  "---\n".
                  "Sent from SantéBlock Contact Form";

    // Try to send email using simple mail() function first
    $headers = array(
        'From' => "SantéBlock Contact Form <<EMAIL>>",
        'Reply-To' => "$name <$email>",
        'X-Mailer' => 'PHP/' . phpversion(),
        'Content-Type' => 'text/plain; charset=UTF-8'
    );

    $headers_string = implode("\r\n", $headers);

    if (mail($to_email, $email_subject, $email_body, $headers_string)) {
        echo json_encode(['success' => true, 'message' => 'Thank you! Your message has been sent successfully.']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Sorry, there was an error sending your message. Please try again later.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid request method.']);
}
?>
