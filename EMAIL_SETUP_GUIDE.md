# Email Setup Guide for SantéBlock Contact Form

## Current Status
Your contact form has been fixed and updated with two email solutions:

### 1. Simple PHP mail() - Currently Active
- **File**: `contact.php`
- **Status**: ✅ Ready to use
- **Requirements**: Basic PHP mail configuration on your server
- **Recipient**: <EMAIL>

### 2. Gmail SMTP with PHPMailer - Advanced Option
- **File**: `send_email.php`
- **Status**: ⚠️ Requires setup
- **Requirements**: PHPMailer library + Gmail App Password

## Quick Test
1. Open your website
2. Go to the Contact section
3. Fill out the form and submit
4. Check if you receive the <NAME_EMAIL>

## If Basic Email Doesn't Work

### Option A: Install PHPMailer (Recommended)
```bash
# Navigate to your project directory
cd c:\xampp\htdocs\web

# Install PHPMailer via Composer
composer require phpmailer/phpmailer
```

### Option B: Manual PHPMailer Setup
1. Download PHPMailer from: https://github.com/PHPMailer/PHPMailer
2. Extract to a `vendor` folder in your project
3. Update the path in `send_email.php`

## Gmail SMTP Setup (For PHPMailer)

### Step 1: Enable 2-Factor Authentication
1. Go to your Google Account settings
2. Enable 2-Factor Authentication

### Step 2: Generate App Password
1. Go to Google Account > Security
2. Under "2-Step Verification", click "App passwords"
3. Generate a new app password for "Mail"
4. Copy the 16-character password

### Step 3: Update Configuration
Edit `send_email.php` and replace:
```php
$mail->Username = '<EMAIL>'; // Your Gmail address
$mail->Password = 'your-app-password';    // The 16-character app password
```

### Step 4: Update Form Action
Change the form to use the new handler:
```javascript
// In script.js, change:
fetch('contact.php', {
// To:
fetch('send_email.php', {
```

## Testing Your Setup

### Test 1: Basic Functionality
1. Fill out the contact form
2. Submit and check for success message
3. Check your email inbox

### Test 2: Error Handling
1. Try submitting with empty fields
2. Try submitting with invalid email
3. Verify error messages appear

## Troubleshooting

### Common Issues:
1. **"Failed to send message"**
   - Check server mail configuration
   - Try the PHPMailer option

2. **"SMTP Error"** (PHPMailer)
   - Verify Gmail credentials
   - Check App Password is correct
   - Ensure 2FA is enabled

3. **Form not submitting**
   - Check browser console for JavaScript errors
   - Verify PHP files are accessible

### Server Requirements:
- PHP 7.0 or higher
- mail() function enabled OR PHPMailer
- CURL extension (for PHPMailer)

## Files Modified:
- ✅ `contact.php` - Updated with better error handling
- ✅ `script.js` - Fixed form submission
- ✅ `index.html` - Removed EmailJS dependencies
- ✅ `send_email.php` - New Gmail SMTP handler (optional)

## Next Steps:
1. Test the current setup
2. If it works, you're done!
3. If not, follow the PHPMailer setup above
4. Consider adding email validation and spam protection
