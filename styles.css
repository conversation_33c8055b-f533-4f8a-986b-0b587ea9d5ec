/* Global Styles */
:root {
    --primary-color: #4CAF50;
    --secondary-color: #2196F3;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --dark-gray: #666;
    --white: #fff;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    background: var(--white);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
}

.logo img {
    height: 40px;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links .app-link {
    color: var(--primary-color);
    font-weight: 600;
}

.nav-links .app-link:hover {
    color: #45a049;
}

.nav-links a:hover {
    color: var(--primary-color);
}

/* Introduction Section */
.introduction {
    padding: 8rem 0 4rem;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
    margin-top: 70px;
}

.intro-wrapper {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.intro-wrapper h1 {
    color: var(--primary-color);
    font-size: 3.5rem;
    margin-bottom: 2rem;
    font-weight: 700;
}

.intro-wrapper p {
    color: var(--dark-gray);
    font-size: 1.4rem;
    line-height: 1.8;
}

@media (max-width: 768px) {
    .introduction {
        padding: 6rem 0 3rem;
    }

    .intro-wrapper h1 {
        font-size: 2.5rem;
    }

    .intro-wrapper p {
        font-size: 1.2rem;
        padding: 0 1rem;
    }
}

/* Problem Section */
.problem {
    padding: 5rem 0;
    background: var(--white);
    margin-top: 0;
}

.problem-content {
    max-width: 900px;
    margin: 0 auto;
}

.problem h2 {
    color: var(--text-color);
    font-size: 2.8rem;
    margin-bottom: 2rem;
    font-weight: 600;
}

.main-issue {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-color);
    margin-bottom: 2.5rem;
}

.problem-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 2.5rem;
}

.stat-group {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-group h3 {
    color: var(--primary-color);
    font-size: 1.4rem;
    margin-bottom: 1.5rem;
}

.stat-group ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.stat-group li {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: var(--text-color);
    display: flex;
    align-items: baseline;
}

.stat-group li span {
    color: var(--secondary-color);
    font-weight: 700;
    font-size: 1.3rem;
    margin-right: 0.5rem;
    min-width: 70px;
}

.privacy-concerns {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.privacy-concerns h3 {
    color: var(--primary-color);
    font-size: 1.4rem;
    margin-bottom: 1rem;
}

.privacy-concerns p {
    color: var(--text-color);
    line-height: 1.8;
    font-size: 1.1rem;
}

@media (max-width: 992px) {
    .problem {
        padding: 4rem 0;
    }

    .problem h2 {
        font-size: 2.4rem;
        text-align: center;
    }

    .main-issue {
        text-align: center;
    }

    .problem-stats {
        grid-template-columns: 1fr;
    }

    .stat-group li {
        font-size: 1rem;
    }

    .stat-group li span {
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .problem {
        padding: 3rem 0;
    }

    .problem h2 {
        font-size: 2rem;
    }

    .main-issue {
        font-size: 1rem;
    }

    .stat-group {
        padding: 1.5rem;
    }

    .stat-group h3 {
        font-size: 1.2rem;
    }

    .privacy-concerns {
        padding: 1.5rem;
    }

    .privacy-concerns h3 {
        font-size: 1.2rem;
    }

    .privacy-concerns p {
        font-size: 1rem;
    }
}

/* Solution Section */
.solution {
    padding: 5rem 0;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
}

.solution h2 {
    text-align: center;
    color: var(--text-color);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.solution-intro {
    max-width: 800px;
    margin: 0 auto 3rem;
    text-align: center;
}

.solution-intro p {
    color: var(--dark-gray);
    font-size: 1.2rem;
    line-height: 1.6;
}

.solution-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 4rem;
}

.solution-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.solution-card:hover {
    transform: translateY(-10px);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.solution-card h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

.solution-card p {
    color: var(--dark-gray);
    margin-bottom: 1.5rem;
    font-size: 1rem;
    line-height: 1.6;
}

.feature-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.feature-list li {
    margin-bottom: 0.8rem;
    padding-left: 1.5rem;
    position: relative;
    color: var(--dark-gray);
}

.feature-list li::before {
    content: "→";
    color: var(--primary-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

.solution-cta {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.solution-cta h3 {
    color: var(--text-color);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.solution-cta p {
    color: var(--dark-gray);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

@media (max-width: 1024px) {
    .solution-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .solution {
        padding: 3rem 0;
    }

    .solution h2 {
        font-size: 2rem;
    }

    .solution-intro p {
        font-size: 1.1rem;
        padding: 0 1rem;
    }

    .solution-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .solution-card {
        padding: 1.5rem;
    }

    .feature-icon {
        font-size: 2rem;
    }

    .solution-card h3 {
        font-size: 1.3rem;
    }

    .solution-cta {
        margin: 0 1rem;
        padding: 1.5rem;
    }

    .solution-cta h3 {
        font-size: 1.5rem;
    }

    .solution-cta p {
        font-size: 1rem;
    }
}

/* Team Section */
.team {
    padding: 5rem 0;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
}

.team h2 {
    text-align: center;
    color: var(--text-color);
    font-size: 2.8rem;
    margin-bottom: 3rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-top: 2rem;
}

.team-member {
    text-align: center;
    padding: 2rem;
    border-radius: 15px;
    background: var(--white);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
}

.member-image {
    width: 200px;
    height: 200px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    overflow: hidden;
    border: 4px solid var(--primary-color);
    position: relative;
    background: var(--light-gray);
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center center;
    display: block;
    transition: transform 0.3s ease;
}

.member-image img:hover {
    transform: scale(1.05);
}

.team-member h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.team-member h4 {
    color: var(--text-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.team-member p {
    color: var(--dark-gray);
    font-size: 0.95rem;
    line-height: 1.6;
    margin: 0 auto;
    max-width: 90%;
}

@media (max-width: 1024px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .team {
        padding: 3rem 0;
    }

    .team h2 {
        font-size: 2rem;
        margin-bottom: 2rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .member-image {
        width: 180px;
        height: 180px;
    }

    .member-image img {
        object-position: center top;
    }

    .team-member {
        padding: 1.5rem;
    }

    .team-member p {
        font-size: 0.9rem;
    }
}

/* Business Model Section */
.business-model {
    padding: 5rem 0;
    background: var(--white);
}

.business-model h2 {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 1.5rem;
}

.business-intro {
    max-width: 1000px;
    margin: 0 auto 3rem;
    text-align: justify;
}

.business-intro p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.8;
}

.section-intro {
    max-width: 900px;
    margin: 0 auto 3rem;
    text-align: center;
    color: var(--dark-gray);
    line-height: 1.8;
}

.business-model-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-top: 3rem;
}

.model-card {
    background: var(--light-gray);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.model-card:hover {
    transform: translateY(-10px);
}

.icon-container {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.icon-container.blue {
    background: rgba(33, 150, 243, 0.1);
}

.icon-container.green {
    background: rgba(76, 175, 80, 0.1);
}

.icon-container.gold {
    background: rgba(255, 193, 7, 0.1);
}

.model-icon {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.model-card h3 {
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-color);
}

.model-card p {
    color: var(--dark-gray);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Financials Section */
.financials {
    padding: 5rem 0;
    background: var(--white);
}

.financials h2 {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 3rem;
}

.financials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.financial-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.financial-card:hover {
    transform: translateY(-10px);
}

.financial-card .icon-container {
    width: 60px;
    height: 60px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.financial-card:hover .icon-container {
    transform: scale(1.1);
}

.financial-card .icon-container i {
    color: var(--text-color);
}

.financial-card .icon-container.blue {
    background: rgba(33, 150, 243, 0.1);
}

.financial-card .icon-container.blue i {
    color: #2196F3;
}

.financial-card .icon-container.green {
    background: rgba(76, 175, 80, 0.1);
}

.financial-card .icon-container.green i {
    color: #4CAF50;
}

.financial-card .icon-container.gold {
    background: rgba(255, 193, 7, 0.1);
}

.financial-card .icon-container.gold i {
    color: #FFC107;
}

.financial-card h3 {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

.financial-card p {
    color: var(--dark-gray);
    line-height: 1.6;
}

/* Revenue Timeline */
.revenue-timeline {
    margin-top: 1.5rem;
}

.timeline-point {
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.timeline-point:before {
    content: "";
    position: absolute;
    left: 0;
    top: 8px;
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
}

.timeline-point .year {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

/* Fundraising Goals */
.fundraising-goals {
    margin-top: 1.5rem;
}

.goal {
    margin-bottom: 1.5rem;
}

.goal .amount {
    color: var(--secondary-color);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    display: block;
}

/* Responsive adjustments for business model section */
@media (max-width: 1024px) {
    .business-model-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .business-model-grid {
        grid-template-columns: 1fr;
    }
    
    .business-model {
        padding: 3rem 0;
    }
    
    .section-intro {
        padding: 0 1rem;
    }
    
    .model-card {
        padding: 1.5rem;
    }
}

/* SanteCheck App Section */
.santecheck {
    padding: 5rem 0;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
}

.santecheck-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.santecheck-text h2 {
    color: var(--text-color);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.santecheck-text p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.santecheck-features ul {
    list-style: none;
    margin-bottom: 2rem;
}

.santecheck-features li {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: var(--text-color);
}

.santecheck-features li::before {
    content: "✓";
    color: var(--primary-color);
    margin-right: 0.5rem;
    font-weight: bold;
}

.santecheck-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.santecheck-cta {
    margin-top: 2rem;
}

.pricing-note {
    margin-top: 1rem;
    font-size: 0.9rem;
    color: var(--dark-gray);
}

/* Responsive adjustments for SanteCheck section */
@media (max-width: 1024px) {
    .santecheck-content {
        gap: 2rem;
    }
    
    .santecheck-text h2 {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .santecheck-content {
        grid-template-columns: 1fr;
    }
    
    .santecheck-image {
        order: -1;
    }
    
    .santecheck-text {
        text-align: center;
    }
    
    .santecheck-features ul {
        display: inline-block;
        text-align: left;
    }
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: var(--white);
}

.contact h2 {
    text-align: center;
    color: var(--text-color);
    font-size: 2.8rem;
    margin-bottom: 2rem;
    font-weight: 600;
}

.contact-content {
    max-width: 600px;
    margin: 0 auto;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    position: relative;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group textarea {
    height: 150px;
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.contact-form .cta-button {
    align-self: center;
    min-width: 200px;
}

@media (max-width: 768px) {
    .contact {
        padding: 3rem 0;
    }

    .contact h2 {
        font-size: 2.2rem;
    }

    .contact-content {
        padding: 0 1rem;
    }

    .form-group input,
    .form-group textarea {
        font-size: 0.9rem;
    }
}

/* Footer */
footer {
    background: var(--text-color);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-logo img {
    height: 40px;
}

.footer-links ul {
    list-style: none;
}

.footer-links a {
    color: var(--white);
    text-decoration: none;
    margin-bottom: 0.5rem;
    display: inline-block;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* CTA Button */
.cta-button {
    display: inline-block;
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: 5px;
    font-weight: 500;
    transition: background 0.3s ease;
    border: none;
    cursor: pointer;
}

.cta-button:hover {
    background: #45a049;
}

/* Mobile Menu Button */
.mobile-menu-button {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-color);
    padding: 0.5rem;
    z-index: 1001;
}

@media (max-width: 768px) {
    .mobile-menu-button {
        display: block;
    }
    
    .nav-links {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--white);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 2rem;
        padding: 2rem;
        z-index: 1000;
        transition: all 0.3s ease;
    }
    
    .nav-links.active {
        display: flex;
    }
    
    .nav-links li {
        opacity: 0;
        transform: translateY(20px);
        animation: fadeInUp 0.5s forwards;
    }
    
    @keyframes fadeInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .nav-links li:nth-child(1) { animation-delay: 0.1s; }
    .nav-links li:nth-child(2) { animation-delay: 0.2s; }
    .nav-links li:nth-child(3) { animation-delay: 0.3s; }
    .nav-links li:nth-child(4) { animation-delay: 0.4s; }
    .nav-links li:nth-child(5) { animation-delay: 0.5s; }
    .nav-links li:nth-child(6) { animation-delay: 0.6s; }
}

/* Competition Section */
.competition {
    padding: 5rem 0;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
}

.competition h2 {
    text-align: center;
    color: var(--text-color);
    margin-bottom: 2rem;
}

.competition-intro {
    max-width: 900px;
    margin: 0 auto 3rem;
    text-align: center;
    line-height: 1.8;
    color: var(--dark-gray);
}

.comparison-table {
    max-width: 1000px;
    margin: 0 auto;
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr repeat(4, 1fr);
    background: var(--text-color);
    color: var(--white);
    padding: 1.5rem;
    font-weight: 600;
    text-align: center;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr repeat(4, 1fr);
    padding: 1.2rem 1.5rem;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
}

.table-row:last-child {
    border-bottom: none;
}

.table-row:hover {
    background-color: rgba(76, 175, 80, 0.05);
}

.company-col {
    color: var(--text-color);
    font-weight: 500;
}

.benefit-col {
    text-align: center;
}

.check {
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: bold;
}

.cross {
    color: #ff4444;
    font-size: 1.5rem;
    font-weight: bold;
}

/* Responsive adjustments for competition section */
@media (max-width: 768px) {
    .competition {
        padding: 3rem 0;
    }

    .competition-intro {
        padding: 0 1rem;
        text-align: left;
    }

    .comparison-table {
        margin: 0 1rem;
        font-size: 0.9rem;
    }

    .table-header, .table-row {
        grid-template-columns: 1.5fr repeat(4, 1fr);
        padding: 1rem;
    }

    .check, .cross {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .table-header, .table-row {
        font-size: 0.8rem;
        padding: 0.8rem;
    }

    .company-col {
        font-size: 0.75rem;
    }
}

/* Customer Profile & Challenges Section */
.customer-profile {
    padding: 5rem 0;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
}

.profile-section {
    max-width: 800px;
    margin: 0 auto 4rem;
    text-align: center;
}

.profile-icon {
    width: 120px;
    height: 120px;
    margin: 0 auto 2rem;
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.profile-icon img {
    width: 60px;
    height: 60px;
    object-fit: contain;
}

.profile-content h2 {
    color: var(--text-color);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.profile-content p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.8;
    max-width: 700px;
    margin: 0 auto;
}

.challenges-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-top: 2rem;
}

.challenge-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.challenge-card:hover {
    transform: translateY(-10px);
}

.challenge-icon {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.challenge-card h3 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.challenge-card p {
    color: var(--dark-gray);
    font-size: 1rem;
    line-height: 1.6;
}

@media (max-width: 992px) {
    .customer-profile {
        padding: 4rem 0;
    }

    .profile-content h2 {
        font-size: 2.2rem;
    }

    .challenges-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .customer-profile {
        padding: 3rem 0;
    }

    .profile-icon {
        width: 100px;
        height: 100px;
    }

    .profile-icon img {
        width: 50px;
        height: 50px;
    }

    .profile-content h2 {
        font-size: 2rem;
    }

    .profile-content p {
        font-size: 1rem;
        padding: 0 1rem;
    }

    .challenge-card {
        padding: 1.5rem;
    }

    .challenge-icon {
        font-size: 2rem;
    }

    .challenge-card h3 {
        font-size: 1.2rem;
    }
}

/* Key Features Section */
.key-features {
    padding: 5rem 0;
    background: var(--white);
}

.key-features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 3rem;
    margin-top: 3rem;
}

.key-feature-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
}

.key-feature-card:hover {
    transform: translateY(-10px);
}

.key-feature-icon {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.8rem;
}

.key-feature-card h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    padding-right: 2rem;
}

.key-feature-card p {
    color: var(--dark-gray);
    line-height: 1.8;
    font-size: 1.1rem;
}

@media (max-width: 992px) {
    .key-features-grid {
        grid-template-columns: 1fr;
        gap: 4rem;
    }
}

@media (max-width: 768px) {
    .key-features {
        padding: 3rem 0;
    }
    
    .key-feature-card {
        padding: 1.5rem;
    }
    
    .key-feature-icon {
        width: 50px;
        height: 50px;
        top: -15px;
        right: -15px;
        font-size: 1.5rem;
    }
    
    .key-feature-card h3 {
        font-size: 1.3rem;
    }
    
    .key-feature-card p {
        font-size: 1rem;
    }
}

/* Market Section */
.market {
    padding: 5rem 0;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
}

.market h2 {
    color: var(--text-color);
    font-size: 2.8rem;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 600;
}

.market-intro {
    max-width: 1000px;
    margin: 0 auto 4rem;
}

.market-intro p {
    color: var(--dark-gray);
    font-size: 1.2rem;
    line-height: 1.8;
    text-align: justify;
}

.market-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
}

.market-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    position: relative;
}

.market-card:hover {
    transform: translateY(-10px);
}

.market-icon {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    text-align: center;
}

.market-card h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.market-card p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.8;
}

@media (max-width: 1024px) {
    .market-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .market {
        padding: 3rem 0;
    }

    .market h2 {
        font-size: 2.2rem;
    }

    .market-intro {
        margin-bottom: 3rem;
        padding: 0 1rem;
    }

    .market-intro p {
        font-size: 1.1rem;
        text-align: left;
    }

    .market-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }

    .market-card {
        padding: 2rem;
    }

    .market-card h3 {
        font-size: 1.3rem;
    }

    .market-card p {
        font-size: 1rem;
    }
}

/* Go-To-Market Strategy Section */
.go-to-market {
    padding: 5rem 0;
    background: linear-gradient(to right, rgba(76, 175, 80, 0.05), rgba(33, 150, 243, 0.05));
}

.go-to-market h2 {
    text-align: center;
    color: var(--text-color);
    font-size: 2.8rem;
    margin-bottom: 2rem;
    font-weight: 600;
}

.gtm-intro {
    max-width: 1000px;
    margin: 0 auto 3rem;
    text-align: justify;
}

.gtm-intro p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.8;
}

.gtm-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
}

.gtm-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.gtm-card:hover {
    transform: translateY(-10px);
}

.gtm-card h3 {
    color: var(--text-color);
    font-size: 1.5rem;
    margin: 1.5rem 0;
    text-align: center;
}

.gtm-card p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.8;
    text-align: center;
}

@media (max-width: 1024px) {
    .gtm-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .go-to-market {
        padding: 3rem 0;
    }

    .go-to-market h2 {
        font-size: 2.2rem;
    }

    .gtm-intro {
        margin-bottom: 2rem;
        padding: 0 1rem;
    }

    .gtm-intro p {
        font-size: 1rem;
    }

    .gtm-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        padding: 0 1rem;
    }

    .gtm-card {
        padding: 2rem;
    }

    .gtm-card h3 {
        font-size: 1.3rem;
    }

    .gtm-card p {
        font-size: 1rem;
    }
}

/* Milestones Section */
.milestones {
    padding: 5rem 0;
    background: var(--white);
}

.milestones h2 {
    text-align: center;
    color: var(--text-color);
    font-size: 2.8rem;
    margin-bottom: 2rem;
    font-weight: 600;
}

.milestones-intro {
    max-width: 1000px;
    margin: 0 auto 4rem;
    text-align: center;
}

.milestones-intro p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.8;
}

.timeline {
    position: relative;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    width: 4px;
    height: 100%;
    background: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 4rem;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    transform: translateX(-50%);
}

.timeline-date {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 500;
}

.timeline-content {
    width: 45%;
    padding: 2rem;
    background: var(--light-gray);
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    position: relative;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-left: auto;
}

.timeline-content h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.timeline-content p {
    color: var(--dark-gray);
    font-size: 1.1rem;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .milestones {
        padding: 3rem 0;
    }

    .milestones h2 {
        font-size: 2.2rem;
    }

    .milestones-intro {
        padding: 0 1rem;
        margin-bottom: 3rem;
    }

    .timeline::before {
        left: 30px;
    }

    .timeline-item {
        margin-bottom: 3rem;
    }

    .timeline-item::before {
        left: 30px;
    }

    .timeline-date {
        left: 30px;
        transform: translateX(-50%);
        font-size: 0.9rem;
    }

    .timeline-content {
        width: calc(100% - 60px);
        margin-left: 60px !important;
    }

    .timeline-content h3 {
        font-size: 1.3rem;
    }

    .timeline-content p {
        font-size: 1rem;
    }
}

/* Section Title Icons */
section h2 i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Specific icon colors */
.market h2 i { color: #4CAF50; }
.business-model h2 i { color: #2196F3; }
.go-to-market h2 i { color: #FF9800; }
.competition h2 i { color: #FFC107; }
.team h2 i { color: #4CAF50; }
.financials h2 i { color: #2196F3; }
.milestones h2 i { color: #4CAF50; }
.santecheck h2 i { color: #2196F3; }
.solution h2 i { color: #FF9800; }
.problem h2 i { color: #f44336; }

@media (max-width: 768px) {
    section h2 i {
        font-size: 0.9em;
    }
}

/* Additional Responsive Improvements */
@media (max-width: 1200px) {
    .container {
        padding: 0 2rem;
    }
}

@media (max-width: 992px) {
    html {
        font-size: 95%;
    }
    
    .intro-wrapper h1 {
        font-size: 3rem;
    }
    
    .intro-wrapper p {
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    html {
        font-size: 90%;
    }
    
    .container {
        padding: 0 1.5rem;
    }
    
    .intro-wrapper h1 {
        font-size: 2.5rem;
    }
    
    .intro-wrapper p {
        font-size: 1.1rem;
        padding: 0 1rem;
    }
    
    section {
        padding: 3rem 0;
    }
    
    .problem-stats,
    .solution-grid,
    .team-grid,
    .market-grid,
    .gtm-grid,
    .key-features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .stat-group,
    .solution-card,
    .team-member,
    .market-card,
    .gtm-card,
    .key-feature-card {
        padding: 1.5rem;
    }
    
    .santecheck-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .santecheck-image {
        order: -1;
    }
    
    .santecheck-image img {
        max-width: 80%;
        margin: 0 auto;
        display: block;
    }
}

@media (max-width: 480px) {
    html {
        font-size: 85%;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .intro-wrapper h1 {
        font-size: 2rem;
    }
    
    .intro-wrapper p {
        font-size: 1rem;
    }
    
    .cta-button {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }
    
    .contact-form {
        padding: 1rem;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 0.8rem;
    }
}

/* Touch Device Improvements */
@media (hover: none) {
    .solution-card:hover,
    .team-member:hover,
    .market-card:hover,
    .gtm-card:hover,
    .key-feature-card:hover {
        transform: none;
    }
    
    .nav-links a {
        padding: 0.8rem;
    }
}

/* High-DPI Screen Improvements */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .logo img,
    .member-image img,
    .santecheck-image img {
        image-rendering: -webkit-optimize-contrast;
    }
}

/* Specific adjustments for team member images */
.member-image img[alt*="Rachel"] {
    object-position: center 20%;
}

.member-image img[alt*="Chrys"] {
    object-position: center 30%;
}

.member-image img[alt*="Franck"] {
    object-position: center center;
}

/* Print Styles */
@media print {
    .navbar,
    .cta-button,
    .contact-form {
        display: none;
    }
    
    * {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
} 